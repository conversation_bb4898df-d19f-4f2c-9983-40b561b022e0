<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BlueStacks Automation Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #1a1a1a;
            color: #fff;
        }

        .navbar {
            background-color: #2d3436 !important;
            border-bottom: 2px solid #0984e3;
        }

        .card {
            background-color: #2d3436;
            border: 1px solid #636e72;
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #0984e3;
            border-bottom: 1px solid #636e72;
        }

        .btn-primary {
            background-color: #0984e3;
            border-color: #0984e3;
        }

        .btn-success {
            background-color: #00b894;
            border-color: #00b894;
        }

        .btn-danger {
            background-color: #e74c3c;
            border-color: #e74c3c;
        }

        .btn-warning {
            background-color: #fdcb6e;
            border-color: #fdcb6e;
            color: #2d3436;
        }

        .status-active {
            color: #00b894;
        }

        .status-inactive {
            color: #e74c3c;
        }

        .log-entry {
            border-bottom: 1px solid #636e72;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        .log-info {
            color: #74b9ff;
        }

        .log-warning {
            color: #fdcb6e;
        }

        .log-error {
            color: #e74c3c;
        }

        .log-debug {
            color: #a29bfe;
        }

        .session-card {
            transition: transform 0.2s;
        }

        .session-card:hover {
            transform: translateY(-2px);
        }

        #logs-container {
            max-height: 400px;
            overflow-y: auto;
            background-color: #1a1a1a;
            border: 1px solid #636e72;
            border-radius: 5px;
            padding: 10px;
        }

        .metric-card {
            text-align: center;
            padding: 20px;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .task-controls {
            border: 1px solid #636e72;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .instance-card {
            border: 1px solid #636e72;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .instance-card:hover {
            border-color: #0984e3;
            transform: translateY(-1px);
        }

        .instance-status {
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        .status-running {
            background-color: #00b894;
            color: white;
        }

        .status-stopped {
            background-color: #e74c3c;
            color: white;
        }

        .status-available {
            background-color: #fdcb6e;
            color: #2d3436;
        }

        .status-offline {
            background-color: #636e72;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Connection Status -->
    <div id="connection-status" class="connection-status">
        <span class="badge bg-danger">Disconnected</span>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-robot"></i> BlueStacks Automation
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="refreshDashboard()">
                    <i class="fas fa-refresh"></i> Refresh
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Status Cards -->
            <div class="col-lg-3 col-md-6">
                <div class="card metric-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-server"></i> Appium Server</h5>
                        <div id="appium-status" class="metric-value status-inactive">Offline</div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card metric-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fab fa-android"></i> BlueStacks</h5>
                        <div id="bluestacks-status" class="metric-value status-inactive">Offline</div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card metric-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-play"></i> Active Sessions</h5>
                        <div id="active-sessions-count" class="metric-value">0</div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="card metric-card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-tasks"></i> Actions</h5>
                        <div id="actions-count" class="metric-value">0</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Session Control -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> Session Control</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">App Package (Optional)</label>
                            <input type="text" class="form-control" id="app-package" placeholder="com.example.app">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Instance Name</label>
                            <input type="text" class="form-control" id="instance-name" value="Pixel">
                        </div>

                        <div class="d-grid gap-2">
                            <button class="btn btn-success" onclick="startSession()">
                                <i class="fas fa-play"></i> Start Session
                            </button>
                            <button class="btn btn-warning" onclick="rotateSession()">
                                <i class="fas fa-sync"></i> Rotate Session
                            </button>
                            <button class="btn btn-danger" onclick="stopSession()">
                                <i class="fas fa-stop"></i> Stop Session
                            </button>
                            <button class="btn btn-danger" onclick="killAllSessions()" style="background-color: #c0392b; border-color: #c0392b;">
                                <i class="fas fa-skull"></i> Kill All Sessions
                            </button>
                        </div>

                        <div id="current-session-info" class="mt-3"></div>
                    </div>
                </div>
            </div>

            <!-- Task Execution -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="task-controls">
                            <h6>Tap</h6>
                            <div class="row">
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm" id="tap-x" placeholder="X">
                                </div>
                                <div class="col-4">
                                    <input type="number" class="form-control form-control-sm" id="tap-y" placeholder="Y">
                                </div>
                                <div class="col-4">
                                    <button class="btn btn-primary btn-sm" onclick="executeTap()">Tap</button>
                                </div>
                            </div>
                        </div>

                        <div class="task-controls">
                            <h6>Scroll</h6>
                            <div class="row">
                                <div class="col-8">
                                    <select class="form-select form-select-sm" id="scroll-direction">
                                        <option value="down">Down</option>
                                        <option value="up">Up</option>
                                        <option value="left">Left</option>
                                        <option value="right">Right</option>
                                    </select>
                                </div>
                                <div class="col-4">
                                    <button class="btn btn-primary btn-sm" onclick="executeScroll()">Scroll</button>
                                </div>
                            </div>
                        </div>

                        <div class="task-controls">
                            <h6>Type Text</h6>
                            <div class="row">
                                <div class="col-8">
                                    <input type="text" class="form-control form-control-sm" id="type-text" placeholder="Text to type">
                                </div>
                                <div class="col-4">
                                    <button class="btn btn-primary btn-sm" onclick="executeType()">Type</button>
                                </div>
                            </div>
                        </div>

                        <div class="task-controls">
                            <h6>Random Activity</h6>
                            <div class="row">
                                <div class="col-8">
                                    <input type="number" class="form-control form-control-sm" id="activity-duration" value="30" placeholder="Duration (seconds)">
                                </div>
                                <div class="col-4">
                                    <button class="btn btn-warning btn-sm" onclick="executeRandomActivity()">Start</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Multi-Instance Manager -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fab fa-android"></i> BlueStacks Instances</h5>
                        <button class="btn btn-sm btn-outline-light" onclick="refreshInstances()">
                            <i class="fas fa-refresh"></i> Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-grid gap-2">
                                <button class="btn btn-danger" onclick="stopAllInstances()">
                                    <i class="fas fa-stop-circle"></i> Stop All Instances
                                </button>
                            </div>
                        </div>

                        <div id="instances-container">
                            <!-- Instances will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Control -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-map-marker-alt"></i> Location Control</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">City</label>
                            <select class="form-select" id="city-select">
                                <option value="">Select City</option>
                                <option value="new_york">New York</option>
                                <option value="london">London</option>
                                <option value="tokyo">Tokyo</option>
                                <option value="paris">Paris</option>
                                <option value="sydney">Sydney</option>
                                <option value="dubai">Dubai</option>
                                <option value="singapore">Singapore</option>
                            </select>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <label class="form-label">Latitude</label>
                                <input type="number" class="form-control" id="custom-lat" step="0.000001" placeholder="40.7128">
                            </div>
                            <div class="col-6">
                                <label class="form-label">Longitude</label>
                                <input type="number" class="form-control" id="custom-lng" step="0.000001" placeholder="-74.0060">
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="setLocationByCity()">Set City Location</button>
                            <button class="btn btn-primary" onclick="setCustomLocation()">Set Custom Location</button>
                            <button class="btn btn-warning" onclick="setRandomLocation()">Random Location</button>
                        </div>

                        <div id="current-location" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Active Sessions -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Session History</h5>
                    </div>
                    <div class="card-body">
                        <div id="sessions-container">
                            <!-- Sessions will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Logs -->
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-file-alt"></i> Logs</h5>
                        <div>
                            <select class="form-select form-select-sm" id="log-level-filter" onchange="filterLogs()">
                                <option value="">All Levels</option>
                                <option value="INFO">Info</option>
                                <option value="WARNING">Warning</option>
                                <option value="ERROR">Error</option>
                                <option value="DEBUG">Debug</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="logs-container">
                            <!-- Logs will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="static/dashboard.js"></script>
</body>
</html>
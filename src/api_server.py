from fastapi import <PERSON><PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import json
import asyncio
import uuid
from datetime import datetime
import os
from loguru import logger

from database import DatabaseManager
from device_manager import DeviceProfileManager
from automation_client import StealthAutomationClient
from location_spoofer import LocationSpoofer


class TaskRequest(BaseModel):
    task_type: str
    parameters: Dict[str, Any]
    app_package: Optional[str] = None


class SessionRequest(BaseModel):
    app_package: Optional[str] = None
    instance_name: str = "Pixel"


class LocationRequest(BaseModel):
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    city_name: Optional[str] = None


class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info("WebSocket client connected")
    
    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        logger.info("WebSocket client disconnected")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
    
    async def broadcast(self, message: Dict):
        if self.active_connections:
            message_str = json.dumps(message)
            for connection in self.active_connections:
                try:
                    await connection.send_text(message_str)
                except:
                    # Remove broken connections
                    self.active_connections.remove(connection)


app = FastAPI(title="BlueStacks Automation Dashboard", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize managers
db_manager = DatabaseManager()
device_manager = DeviceProfileManager()
automation_client = None
location_spoofer = LocationSpoofer()
connection_manager = ConnectionManager()


def get_automation_client():
    global automation_client
    if automation_client is None:
        automation_client = StealthAutomationClient(device_manager)
    return automation_client


@app.get("/")
async def read_root():
    """Serve the dashboard HTML"""
    return FileResponse("dashboard/index.html")


@app.get("/api/status")
async def get_status():
    """Get overall system status"""
    client = get_automation_client()
    
    return {
        "appium_server": device_manager.appium_manager.get_server_status(),
        "bluestacks": {
            "running": True,  # This should be checked dynamically
            "current_profile": device_manager.bluestacks_manager.get_current_profile()
        },
        "automation_client": {
            "active": client.current_session_id is not None,
            "session_id": client.current_session_id,
            "action_count": client.action_count
        },
        "database": {
            "connected": True,  # Should check database connection
            "active_sessions": len(db_manager.get_active_sessions())
        }
    }


@app.get("/api/sessions")
async def get_sessions():
    """Get all active and recent sessions"""
    active_sessions = db_manager.get_active_sessions()
    session_history = db_manager.get_session_history(limit=20)
    
    return {
        "active_sessions": active_sessions,
        "session_history": session_history
    }


@app.get("/api/sessions/{session_id}")
async def get_session_details(session_id: str):
    """Get detailed information about a specific session"""
    session_logs = db_manager.get_session_logs(session_id, limit=100)
    session_tasks = db_manager.get_session_tasks(session_id)
    
    return {
        "session_id": session_id,
        "logs": session_logs,
        "tasks": session_tasks
    }


@app.post("/api/sessions/start")
async def start_automation_session(request: SessionRequest):
    """Start a new automation session"""
    try:
        client = get_automation_client()
        
        success = client.start_session(request.app_package, request.instance_name)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to start automation session")
        
        # Log session creation in database
        session_info = client.get_session_info()
        if session_info:
            db_manager.create_automation_session(session_info)
            db_manager.log_automation_event(
                session_info['session_id'], 
                "INFO", 
                "Automation session started"
            )
        
        # Broadcast update to WebSocket clients
        await connection_manager.broadcast({
            "type": "session_started",
            "data": session_info
        })
        
        return {"success": True, "session_info": session_info}
        
    except Exception as e:
        logger.error(f"Failed to start session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/sessions/stop")
async def stop_automation_session():
    """Stop the current automation session"""
    try:
        client = get_automation_client()
        
        if client.current_session_id:
            session_id = client.current_session_id
            client.end_session()
            
            # Update database
            db_manager.update_session_status(session_id, "completed", datetime.utcnow())
            db_manager.log_automation_event(session_id, "INFO", "Automation session stopped")
            
            # Broadcast update
            await connection_manager.broadcast({
                "type": "session_stopped",
                "data": {"session_id": session_id}
            })
            
            return {"success": True, "message": "Session stopped"}
        else:
            return {"success": False, "message": "No active session"}
            
    except Exception as e:
        logger.error(f"Failed to stop session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/sessions/rotate")
async def rotate_automation_session(request: SessionRequest):
    """Rotate the current automation session for anti-detection"""
    try:
        client = get_automation_client()
        
        if not client.current_session_id:
            return await start_automation_session(request)
        
        old_session_id = client.current_session_id
        success = client.rotate_session(request.app_package, request.instance_name)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to rotate session")
        
        # Update database
        db_manager.update_session_status(old_session_id, "rotated", datetime.utcnow())
        
        new_session_info = client.get_session_info()
        if new_session_info:
            db_manager.create_automation_session(new_session_info)
            db_manager.log_automation_event(
                new_session_info['session_id'], 
                "INFO", 
                f"Session rotated from {old_session_id}"
            )
        
        # Broadcast update
        await connection_manager.broadcast({
            "type": "session_rotated",
            "data": {
                "old_session_id": old_session_id,
                "new_session_info": new_session_info
            }
        })
        
        return {"success": True, "session_info": new_session_info}
        
    except Exception as e:
        logger.error(f"Failed to rotate session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/tasks/execute")
async def execute_task(request: TaskRequest):
    """Execute a specific automation task"""
    try:
        client = get_automation_client()
        
        if not client.current_session_id:
            raise HTTPException(status_code=400, detail="No active automation session")
        
        task_id = str(uuid.uuid4())
        task_name = f"{request.task_type}_{int(datetime.now().timestamp())}"
        
        # Log task creation
        task_data = {
            "task_id": task_id,
            "session_id": client.current_session_id,
            "task_name": task_name,
            "task_type": request.task_type,
            "parameters": request.parameters,
            "start_time": datetime.utcnow()
        }
        
        db_manager.create_automation_task(task_data)
        db_manager.update_task_status(task_id, "running")
        
        # Execute task based on type
        result = await execute_automation_task(client, request.task_type, request.parameters)
        
        # Update task status
        end_time = datetime.utcnow()
        if result["success"]:
            db_manager.update_task_status(task_id, "completed", end_time)
        else:
            db_manager.update_task_status(task_id, "failed", end_time, result.get("error"))
        
        # Log task completion
        db_manager.log_automation_event(
            client.current_session_id,
            "INFO" if result["success"] else "ERROR",
            f"Task {request.task_type} {'completed' if result['success'] else 'failed'}",
            task_id
        )
        
        # Broadcast update
        await connection_manager.broadcast({
            "type": "task_completed",
            "data": {
                "task_id": task_id,
                "task_type": request.task_type,
                "success": result["success"],
                "result": result
            }
        })
        
        return {"success": True, "task_id": task_id, "result": result}
        
    except Exception as e:
        logger.error(f"Failed to execute task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def execute_automation_task(client: StealthAutomationClient, task_type: str, parameters: Dict) -> Dict:
    """Execute the actual automation task"""
    try:
        if task_type == "tap":
            x = parameters.get("x")
            y = parameters.get("y")
            if x is None or y is None:
                return {"success": False, "error": "Missing x or y coordinates"}
            
            success = client.human_tap(x, y)
            return {"success": success}
            
        elif task_type == "swipe":
            start_x = parameters.get("start_x")
            start_y = parameters.get("start_y")
            end_x = parameters.get("end_x")
            end_y = parameters.get("end_y")
            
            if None in [start_x, start_y, end_x, end_y]:
                return {"success": False, "error": "Missing swipe coordinates"}
            
            success = client.human_swipe(start_x, start_y, end_x, end_y)
            return {"success": success}
            
        elif task_type == "scroll":
            direction = parameters.get("direction", "down")
            success = client.human_scroll(direction)
            return {"success": success}
            
        elif task_type == "type":
            text = parameters.get("text")
            if not text:
                return {"success": False, "error": "Missing text parameter"}
            
            success = client.human_type(text)
            return {"success": success}
            
        elif task_type == "random_activity":
            duration = parameters.get("duration", 30)
            client.perform_random_human_activity(duration)
            return {"success": True}
            
        else:
            return {"success": False, "error": f"Unknown task type: {task_type}"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}


@app.post("/api/location/set")
async def set_location(request: LocationRequest):
    """Set device location"""
    try:
        if request.city_name:
            success = location_spoofer.set_city_location(request.city_name)
        elif request.latitude is not None and request.longitude is not None:
            success = location_spoofer.set_location(request.latitude, request.longitude)
        else:
            success = location_spoofer.set_random_location()
        
        if success:
            current_location = location_spoofer.get_current_location()
            
            # Save to database if there's an active session
            client = get_automation_client()
            if client.current_session_id and current_location:
                db_manager.save_location_point({
                    "session_id": client.current_session_id,
                    "latitude": current_location.latitude,
                    "longitude": current_location.longitude,
                    "altitude": current_location.altitude,
                    "accuracy": current_location.accuracy
                })
            
            # Broadcast update
            await connection_manager.broadcast({
                "type": "location_updated",
                "data": {
                    "latitude": current_location.latitude,
                    "longitude": current_location.longitude
                }
            })
            
            return {"success": True, "location": current_location.__dict__}
        else:
            raise HTTPException(status_code=500, detail="Failed to set location")
            
    except Exception as e:
        logger.error(f"Failed to set location: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/logs")
async def get_logs(limit: int = 100, level: Optional[str] = None):
    """Get recent logs"""
    logs = db_manager.get_recent_logs(limit=limit, level=level)
    return {"logs": logs}


@app.get("/api/device-profiles")
async def get_device_profiles():
    """Get available device profiles"""
    try:
        config_path = "config/device_profiles.yaml"
        
        # Try relative to current directory first
        if os.path.exists(config_path):
            config_file = config_path
        else:
            # Try relative to project root (one level up from src)
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_file = os.path.join(project_root, config_path)
            
        with open(config_file, 'r') as f:
            import yaml
            config = yaml.safe_load(f)
        
        return {
            "device_profiles": list(config.get('device_profiles', {}).keys()),
            "locations": list(config.get('locations', {}).keys())
        }
    except Exception as e:
        logger.error(f"Failed to get device profiles: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await connection_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Echo received data for now
            await connection_manager.send_personal_message(f"Received: {data}", websocket)
    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)


# Mount static files for dashboard
app.mount("/static", StaticFiles(directory="dashboard/static"), name="static")


if __name__ == "__main__":
    import uvicorn
    
    host = os.getenv('DASHBOARD_HOST', '0.0.0.0')
    port = int(os.getenv('DASHBOARD_PORT', 8000))
    
    logger.info(f"Starting dashboard server on {host}:{port}")
    uvicorn.run(app, host=host, port=port)
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Float, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.dialects.postgresql import JSO<PERSON>
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
import json
import os
from loguru import logger

Base = declarative_base()


class AutomationSession(Base):
    __tablename__ = "automation_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String, unique=True, index=True)
    device_name = Column(String)
    device_model = Column(String)
    platform_version = Column(String)
    android_id = Column(String)
    imei = Column(String)
    advertising_id = Column(String)
    location_name = Column(String)
    latitude = Column(Float)
    longitude = Column(Float)
    start_time = Column(DateTime, default=datetime.utcnow)
    end_time = Column(DateTime)
    status = Column(String, default="active")  # active, completed, failed, terminated
    actions_count = Column(Integer, default=0)
    app_package = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)


class AutomationTask(Base):
    __tablename__ = "automation_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String, unique=True, index=True)
    session_id = Column(String, index=True)
    task_name = Column(String)
    task_type = Column(String)  # tap, swipe, type, scroll, etc.
    parameters = Column(Text)  # JSON string of task parameters
    status = Column(String, default="pending")  # pending, running, completed, failed
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    duration_seconds = Column(Float)
    error_message = Column(Text)
    screenshot_path = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)


class AutomationLog(Base):
    __tablename__ = "automation_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String, index=True)
    task_id = Column(String, index=True)
    level = Column(String)  # INFO, WARNING, ERROR, DEBUG
    message = Column(Text)
    details = Column(Text)  # JSON string with additional details
    timestamp = Column(DateTime, default=datetime.utcnow)


class DeviceFingerprint(Base):
    __tablename__ = "device_fingerprints"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String, index=True)
    android_id = Column(String)
    imei = Column(String)
    advertising_id = Column(String)
    device_model = Column(String)
    manufacturer = Column(String)
    platform_version = Column(String)
    screen_resolution = Column(String)
    dpi = Column(Integer)
    user_agent = Column(String)
    timezone = Column(String)
    language = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)


class LocationHistory(Base):
    __tablename__ = "location_history"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(String, index=True)
    latitude = Column(Float)
    longitude = Column(Float)
    altitude = Column(Float, default=0.0)
    accuracy = Column(Float)
    speed = Column(Float, default=0.0)
    bearing = Column(Float, default=0.0)
    timestamp = Column(DateTime, default=datetime.utcnow)


class DatabaseManager:
    def __init__(self, database_url: str = None):
        if database_url is None:
            database_url = os.getenv('DATABASE_URL', 'sqlite:///automation.db')
        
        self.engine = create_engine(database_url, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Create tables
        Base.metadata.create_all(bind=self.engine)
        logger.info(f"Database initialized: {database_url}")
    
    def get_session(self) -> Session:
        return self.SessionLocal()
    
    def create_automation_session(self, session_data: Dict) -> bool:
        try:
            with self.get_session() as db:
                session = AutomationSession(
                    session_id=session_data['session_id'],
                    device_name=session_data.get('device_name'),
                    device_model=session_data.get('device_model'),
                    platform_version=session_data.get('platform_version'),
                    android_id=session_data.get('android_id'),
                    imei=session_data.get('imei'),
                    advertising_id=session_data.get('advertising_id'),
                    location_name=session_data.get('location_name'),
                    latitude=session_data.get('latitude'),
                    longitude=session_data.get('longitude'),
                    app_package=session_data.get('app_package'),
                    start_time=session_data.get('start_time', datetime.utcnow())
                )
                
                db.add(session)
                db.commit()
                logger.info(f"Created automation session: {session_data['session_id']}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to create automation session: {e}")
            return False
    
    def update_session_status(self, session_id: str, status: str, end_time: datetime = None) -> bool:
        try:
            with self.get_session() as db:
                session = db.query(AutomationSession).filter(
                    AutomationSession.session_id == session_id
                ).first()
                
                if session:
                    session.status = status
                    if end_time:
                        session.end_time = end_time
                    db.commit()
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Failed to update session status: {e}")
            return False
    
    def increment_action_count(self, session_id: str) -> bool:
        try:
            with self.get_session() as db:
                session = db.query(AutomationSession).filter(
                    AutomationSession.session_id == session_id
                ).first()
                
                if session:
                    session.actions_count += 1
                    db.commit()
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Failed to increment action count: {e}")
            return False
    
    def create_automation_task(self, task_data: Dict) -> bool:
        try:
            with self.get_session() as db:
                task = AutomationTask(
                    task_id=task_data['task_id'],
                    session_id=task_data['session_id'],
                    task_name=task_data['task_name'],
                    task_type=task_data.get('task_type'),
                    parameters=json.dumps(task_data.get('parameters', {})),
                    start_time=task_data.get('start_time', datetime.utcnow())
                )
                
                db.add(task)
                db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to create automation task: {e}")
            return False
    
    def update_task_status(self, task_id: str, status: str, end_time: datetime = None,
                          error_message: str = None) -> bool:
        try:
            with self.get_session() as db:
                task = db.query(AutomationTask).filter(
                    AutomationTask.task_id == task_id
                ).first()
                
                if task:
                    task.status = status
                    if end_time:
                        task.end_time = end_time
                        if task.start_time:
                            task.duration_seconds = (end_time - task.start_time).total_seconds()
                    if error_message:
                        task.error_message = error_message
                    db.commit()
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Failed to update task status: {e}")
            return False
    
    def log_automation_event(self, session_id: str, level: str, message: str,
                           task_id: str = None, details: Dict = None) -> bool:
        try:
            with self.get_session() as db:
                log_entry = AutomationLog(
                    session_id=session_id,
                    task_id=task_id,
                    level=level,
                    message=message,
                    details=json.dumps(details) if details else None
                )
                
                db.add(log_entry)
                db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to log automation event: {e}")
            return False
    
    def save_device_fingerprint(self, fingerprint_data: Dict) -> bool:
        try:
            with self.get_session() as db:
                fingerprint = DeviceFingerprint(
                    session_id=fingerprint_data['session_id'],
                    android_id=fingerprint_data.get('android_id'),
                    imei=fingerprint_data.get('imei'),
                    advertising_id=fingerprint_data.get('advertising_id'),
                    device_model=fingerprint_data.get('device_model'),
                    manufacturer=fingerprint_data.get('manufacturer'),
                    platform_version=fingerprint_data.get('platform_version'),
                    screen_resolution=fingerprint_data.get('screen_resolution'),
                    dpi=fingerprint_data.get('dpi'),
                    user_agent=fingerprint_data.get('user_agent'),
                    timezone=fingerprint_data.get('timezone'),
                    language=fingerprint_data.get('language')
                )
                
                db.add(fingerprint)
                db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to save device fingerprint: {e}")
            return False
    
    def save_location_point(self, location_data: Dict) -> bool:
        try:
            with self.get_session() as db:
                location = LocationHistory(
                    session_id=location_data['session_id'],
                    latitude=location_data['latitude'],
                    longitude=location_data['longitude'],
                    altitude=location_data.get('altitude', 0.0),
                    accuracy=location_data.get('accuracy', 10.0),
                    speed=location_data.get('speed', 0.0),
                    bearing=location_data.get('bearing', 0.0)
                )
                
                db.add(location)
                db.commit()
                return True
                
        except Exception as e:
            logger.error(f"Failed to save location point: {e}")
            return False
    
    def get_active_sessions(self) -> List[Dict]:
        try:
            with self.get_session() as db:
                sessions = db.query(AutomationSession).filter(
                    AutomationSession.status == "active"
                ).all()
                
                return [{
                    'session_id': s.session_id,
                    'device_name': s.device_name,
                    'device_model': s.device_model,
                    'platform_version': s.platform_version,
                    'location_name': s.location_name,
                    'start_time': s.start_time.isoformat() if s.start_time else None,
                    'actions_count': s.actions_count,
                    'app_package': s.app_package
                } for s in sessions]
                
        except Exception as e:
            logger.error(f"Failed to get active sessions: {e}")
            return []
    
    def get_session_history(self, limit: int = 50) -> List[Dict]:
        try:
            with self.get_session() as db:
                sessions = db.query(AutomationSession).order_by(
                    AutomationSession.created_at.desc()
                ).limit(limit).all()
                
                return [{
                    'session_id': s.session_id,
                    'device_name': s.device_name,
                    'device_model': s.device_model,
                    'platform_version': s.platform_version,
                    'location_name': s.location_name,
                    'start_time': s.start_time.isoformat() if s.start_time else None,
                    'end_time': s.end_time.isoformat() if s.end_time else None,
                    'status': s.status,
                    'actions_count': s.actions_count,
                    'app_package': s.app_package,
                    'duration_seconds': (s.end_time - s.start_time).total_seconds() if s.end_time and s.start_time else None
                } for s in sessions]
                
        except Exception as e:
            logger.error(f"Failed to get session history: {e}")
            return []
    
    def get_session_logs(self, session_id: str, limit: int = 100) -> List[Dict]:
        try:
            with self.get_session() as db:
                logs = db.query(AutomationLog).filter(
                    AutomationLog.session_id == session_id
                ).order_by(AutomationLog.timestamp.desc()).limit(limit).all()
                
                return [{
                    'level': log.level,
                    'message': log.message,
                    'details': json.loads(log.details) if log.details else None,
                    'timestamp': log.timestamp.isoformat() if log.timestamp else None,
                    'task_id': log.task_id
                } for log in logs]
                
        except Exception as e:
            logger.error(f"Failed to get session logs: {e}")
            return []
    
    def get_recent_logs(self, limit: int = 100, level: str = None) -> List[Dict]:
        try:
            with self.get_session() as db:
                query = db.query(AutomationLog)
                
                if level:
                    query = query.filter(AutomationLog.level == level)
                
                logs = query.order_by(AutomationLog.timestamp.desc()).limit(limit).all()
                
                return [{
                    'session_id': log.session_id,
                    'level': log.level,
                    'message': log.message,
                    'details': json.loads(log.details) if log.details else None,
                    'timestamp': log.timestamp.isoformat() if log.timestamp else None,
                    'task_id': log.task_id
                } for log in logs]
                
        except Exception as e:
            logger.error(f"Failed to get recent logs: {e}")
            return []
    
    def get_session_tasks(self, session_id: str) -> List[Dict]:
        try:
            with self.get_session() as db:
                tasks = db.query(AutomationTask).filter(
                    AutomationTask.session_id == session_id
                ).order_by(AutomationTask.created_at.desc()).all()
                
                return [{
                    'task_id': task.task_id,
                    'task_name': task.task_name,
                    'task_type': task.task_type,
                    'parameters': json.loads(task.parameters) if task.parameters else None,
                    'status': task.status,
                    'start_time': task.start_time.isoformat() if task.start_time else None,
                    'end_time': task.end_time.isoformat() if task.end_time else None,
                    'duration_seconds': task.duration_seconds,
                    'error_message': task.error_message
                } for task in tasks]
                
        except Exception as e:
            logger.error(f"Failed to get session tasks: {e}")
            return []
    
    def cleanup_old_data(self, days_old: int = 30) -> bool:
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            with self.get_session() as db:
                # Delete old sessions and related data
                db.query(AutomationLog).filter(
                    AutomationLog.timestamp < cutoff_date
                ).delete()
                
                db.query(AutomationTask).filter(
                    AutomationTask.created_at < cutoff_date
                ).delete()
                
                db.query(LocationHistory).filter(
                    LocationHistory.timestamp < cutoff_date
                ).delete()
                
                db.query(DeviceFingerprint).filter(
                    DeviceFingerprint.created_at < cutoff_date
                ).delete()
                
                db.query(AutomationSession).filter(
                    AutomationSession.created_at < cutoff_date
                ).delete()
                
                db.commit()
                logger.info(f"Cleaned up data older than {days_old} days")
                return True
                
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")
            return False
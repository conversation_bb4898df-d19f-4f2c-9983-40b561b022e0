import subprocess
import time
import requests
import random
import threading
import os
import signal
import platform
from typing import Dict, Optional, List
from loguru import logger


class AppiumServerManager:
    def __init__(self, host: str = "127.0.0.1", port: int = 4723):
        self.host = host
        self.port = port
        self.process = None
        self.is_running = False
        self.capabilities_pool = []
        self.current_capabilities = None
    
    def _log_subprocess_output(self, pipe, log_func):
        """Reads and logs output from a subprocess pipe."""
        try:
            for line in iter(pipe.readline, ''):
                if line.strip():  # Only log non-empty lines
                    log_func(f"Appium: {line.strip()}")
        except Exception as e:
            logger.debug(f"Error reading subprocess output: {e}")
        finally:
            pipe.close()
        
    def start_server(self, log_level: str = "error") -> bool:
        if self.is_running:
            logger.warning("Appium server is already running")
            return True
            
        try:
            # Check if Appium is installed and working
            try:
                # First check if appium command is in PATH
                import shutil
                appium_path = shutil.which("appium")
                if not appium_path:
                    logger.error("Appium command not found in PATH. Install with: npm install -g appium")
                    logger.error("Make sure npm global directory is in your PATH")
                    return False
                logger.info(f"Appium executable found at: {appium_path}")
                
                # Test the command manually first
                result = subprocess.run(["appium", "--version"], capture_output=True, text=True, timeout=10)
                if result.returncode != 0:
                    logger.error(f"Appium version check failed: {result.stderr}")
                    logger.error("Try running 'appium --version' manually to debug")
                    return False
                logger.info(f"Appium found: {result.stdout.strip()}")
                
                # Setup Android SDK environment
                self._setup_android_environment()
                
                # Check if UiAutomator2 driver is installed
                self._check_uiautomator2_driver()
                
            except FileNotFoundError:
                logger.error("Appium not installed. Install with: npm install -g appium")
                logger.error("Make sure Node.js and npm are installed first")
                return False
            except subprocess.TimeoutExpired:
                logger.error("Appium version check timed out - this may indicate environment issues")
                return False
            except Exception as e:
                logger.error(f"Appium version check failed: {e}")
                return False
            
            # Kill any existing Appium processes
            self._kill_existing_appium()
            
            # Updated command for Appium 2.x compatibility
            command = [
                "appium",
                "--address", self.host,
                "--port", str(self.port),
                "--log-level", log_level,
                "--session-override",
                "--allow-insecure", "chromedriver_autodownload"
            ]
            
            logger.info(f"Starting Appium server with command: {' '.join(command)}")
            
            try:
                self.process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,  # Use text=True instead of universal_newlines
                    bufsize=1   # Line-buffered
                )

                # Start logging threads to see Appium output in real-time
                threading.Thread(target=self._log_subprocess_output, 
                               args=(self.process.stdout, logger.info), daemon=True).start()
                threading.Thread(target=self._log_subprocess_output, 
                               args=(self.process.stderr, logger.error), daemon=True).start()

                # Wait for server to start with extended timeout
                max_attempts = 60
                logger.info("Waiting for Appium server to start...")
                
                for attempt in range(max_attempts):
                    # Check if process terminated unexpectedly
                    if self.process.poll() is not None:
                        logger.error("Appium process terminated unexpectedly. Check logs above for details.")
                        return False

                    if self._is_server_running():
                        self.is_running = True
                        logger.info(f"Appium server started successfully after {attempt + 1} seconds")
                        return True
                    
                    # Log progress every 15 seconds
                    if (attempt + 1) % 15 == 0:
                        logger.info(f"Still waiting for Appium server... ({attempt + 1}/{max_attempts} seconds)")
                    
                    time.sleep(1)
                    
                logger.error(f"Appium server failed to start within {max_attempts} seconds. It may have timed out or an error occurred.")
                self.stop_server()
                return False
                
            except Exception as e:
                logger.error(f"Appium server startup failed with an exception: {e}")
                return False
            
        except Exception as e:
            logger.error(f"Appium server startup failed: {e}")
            return False
    
    def _check_uiautomator2_driver(self):
        """Check if UiAutomator2 driver is installed"""
        try:
            result = subprocess.run(["appium", "driver", "list", "--installed"], 
                                  capture_output=True, text=True, timeout=10)
            logger.debug(f"Installed drivers output: {result.stdout}")
            logger.debug(f"Installed drivers stderr: {result.stderr}")
            
            # Check both stdout and stderr for driver info (Appium outputs to different streams)
            output_text = (result.stdout + result.stderr).lower()
            if "uiautomator2" not in output_text:
                logger.warning("UiAutomator2 driver not found. Installing...")
                install_result = subprocess.run(["appium", "driver", "install", "uiautomator2"], 
                                              capture_output=True, text=True, timeout=60)
                if install_result.returncode == 0:
                    logger.info("UiAutomator2 driver installed successfully")
                else:
                    logger.error(f"Failed to install UiAutomator2 driver: {install_result.stderr}")
            else:
                logger.info("UiAutomator2 driver is already installed")
        except Exception as e:
            logger.warning(f"Could not check UiAutomator2 driver: {e}")
    
    def _setup_android_environment(self):
        """Setup Android SDK environment variables if not already set"""
        try:
            # Check if Android SDK is already configured
            if os.environ.get('ANDROID_HOME') or os.environ.get('ANDROID_SDK_ROOT'):
                logger.info("Android SDK environment already configured")
                return True
            
            # Check if ADB is available and use its parent directory as SDK
            try:
                adb_result = subprocess.run(['which', 'adb'], capture_output=True, text=True)
                if adb_result.returncode == 0:
                    adb_path = adb_result.stdout.strip()
                    # If ADB is from homebrew, create a minimal Android SDK setup
                    if '/opt/homebrew' in adb_path or '/usr/local' in adb_path:
                        # Use homebrew's location as SDK root
                        sdk_root = os.path.dirname(adb_path)
                        os.environ['ANDROID_HOME'] = sdk_root
                        os.environ['ANDROID_SDK_ROOT'] = sdk_root
                        logger.info(f"Android SDK configured using ADB location: {sdk_root}")
                        return True
            except:
                pass
            
            # Common Android SDK paths on macOS
            possible_sdk_paths = [
                os.path.expanduser('~/Library/Android/sdk'),
                os.path.expanduser('~/Android/Sdk'),
                '/usr/local/share/android-sdk',
                '/opt/homebrew/share/android-sdk'
            ]
            
            android_sdk_path = None
            for path in possible_sdk_paths:
                if os.path.exists(path):
                    android_sdk_path = path
                    break
            
            if android_sdk_path:
                os.environ['ANDROID_HOME'] = android_sdk_path
                os.environ['ANDROID_SDK_ROOT'] = android_sdk_path
                # Add platform-tools to PATH
                platform_tools = os.path.join(android_sdk_path, 'platform-tools')
                if os.path.exists(platform_tools):
                    current_path = os.environ.get('PATH', '')
                    if platform_tools not in current_path:
                        os.environ['PATH'] = f"{platform_tools}:{current_path}"
                
                logger.info(f"Android SDK configured: {android_sdk_path}")
                return True
            else:
                logger.warning("Full Android SDK not found, but ADB is available")
                logger.info("This should be sufficient for basic Appium operations")
                # Set a minimal SDK path for Appium to work
                os.environ['ANDROID_HOME'] = '/tmp/android-sdk-minimal'
                os.environ['ANDROID_SDK_ROOT'] = '/tmp/android-sdk-minimal'
                return True
                
        except Exception as e:
            logger.error(f"Failed to setup Android environment: {e}")
            return False

    def _kill_existing_appium(self):
        """Kills existing Appium processes in a cross-platform way."""
        try:
            system = platform.system()
            logger.info(f"Attempting to kill existing Appium processes on {system}...")
            
            if system == "Windows":
                # For Windows - kill node.exe processes (Appium runs on Node.js)
                commands = [
                    "taskkill /F /IM node.exe /T",
                    "netstat -ano | findstr :4723"  # Check what's using port 4723
                ]
                for cmd in commands:
                    try:
                        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                        if "netstat" in cmd and result.stdout:
                            logger.info(f"Port 4723 usage: {result.stdout.strip()}")
                    except:
                        pass
            else:
                # For Linux and macOS
                commands = [
                    ["pkill", "-f", "appium"],
                    ["lsof", "-ti:4723"]  # Check what's using port 4723
                ]
                for cmd in commands:
                    try:
                        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                        if "lsof" in cmd[0] and result.stdout:
                            # Kill process using port 4723
                            pid = result.stdout.strip()
                            if pid:
                                subprocess.run(["kill", "-9", pid], capture_output=True)
                                logger.info(f"Killed process {pid} using port 4723")
                    except:
                        pass
                        
            time.sleep(2)  # Give time for processes to terminate
            logger.info("Process cleanup completed.")
            
        except Exception as e:
            logger.warning(f"Could not execute kill command (this is often okay): {e}")
    
    def _is_server_running(self) -> bool:
        try:
            # Try multiple endpoints that Appium 2.x might respond to
            endpoints = [
                f"http://{self.host}:{self.port}/status",  # Appium 2.x status endpoint
                f"http://{self.host}:{self.port}/wd/hub/status",  # Legacy endpoint
                f"http://{self.host}:{self.port}/sessions"  # Alternative endpoint
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(endpoint, timeout=3)
                    if response.status_code in [200, 404]:  # 404 is OK for some endpoints when no sessions exist
                        logger.debug(f"Appium server responding on {endpoint} (status: {response.status_code})")
                        return True
                except requests.exceptions.RequestException:
                    continue
            
            return False
        except:
            return False
    
    def stop_server(self) -> bool:
        try:
            if self.process:
                self.process.terminate()
                try:
                    self.process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                    self.process.wait()
                
                self.process = None
                
            self._kill_existing_appium()
            self.is_running = False
            logger.info("Appium server stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop Appium server: {e}")
            return False
    
    def restart_server(self) -> bool:
        logger.info("Restarting Appium server")
        self.stop_server()
        time.sleep(3)
        return self.start_server()
    
    def generate_capabilities(self, device_profile: Dict, app_package: str = None) -> Dict:
        base_capabilities = {
            "platformName": device_profile["platformName"],
            "platformVersion": device_profile["platformVersion"],
            "deviceName": device_profile["deviceName"],
            "udid": device_profile["udid"],
            "automationName": device_profile["automationName"],
            "newCommandTimeout": 300,
            "uiautomator2ServerInstallTimeout": 60000,
            "uiautomator2ServerLaunchTimeout": 60000,
            "autoGrantPermissions": True,
            "noReset": False,
            "fullReset": False,
            "skipDeviceInitialization": False,
            "skipServerInstallation": False,
        }
        
        # Add anti-detection capabilities (simplified for modern Appium)
        anti_detection_caps = {
            "ignoreUnimportantViews": True,
            "allowInvisibleElements": True,
            "enableNotificationListener": False,
            "actionAcknowledgmentTimeout": random.randint(2000, 5000),
            "keyInjectionDelay": random.randint(100, 300),
            "scrollAcknowledgmentTimeout": random.randint(1000, 3000),
            "shouldUseCompactResponses": True,
            "elementResponseAttributes": "name,text,displayed,enabled",
            "shutdownOnPowerDisconnect": False,
        }
        
        base_capabilities.update(anti_detection_caps)
        
        # Add app-specific capabilities if provided
        if app_package:
            base_capabilities.update({
                "appPackage": app_package,
                "appActivity": "MainActivity",  # This should be customized per app
                "autoLaunch": True,
            })
        
        # Add chrome options for web contexts (simplified for modern Appium)
        base_capabilities["chromeOptions"] = {
            "args": [
                "--disable-blink-features=AutomationControlled",
                "--disable-extensions",
                "--no-sandbox",
                "--disable-dev-shm-usage"
            ]
        }
        
        self.current_capabilities = base_capabilities
        logger.info("Generated new capabilities with anti-detection features")
        return base_capabilities
    
    def get_session_info(self) -> Optional[Dict]:
        try:
            response = requests.get(f"http://{self.host}:{self.port}/wd/hub/sessions")
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            logger.error(f"Failed to get session info: {e}")
            return None
    
    def delete_all_sessions(self) -> bool:
        try:
            sessions = self.get_session_info()
            if sessions and 'value' in sessions:
                for session in sessions['value']:
                    session_id = session['id']
                    delete_url = f"http://{self.host}:{self.port}/wd/hub/session/{session_id}"
                    requests.delete(delete_url)
                    logger.info(f"Deleted session: {session_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete sessions: {e}")
            return False
    
    def rotate_capabilities(self, device_profile: Dict, app_package: str = None) -> Dict:
        logger.info("Rotating capabilities for anti-detection")
        
        # Delete existing sessions
        self.delete_all_sessions()
        time.sleep(2)
        
        # Generate new capabilities
        new_caps = self.generate_capabilities(device_profile, app_package)
        
        # Add random delays and behaviors
        random_settings = {
            "settings[waitForIdleTimeout]": random.randint(500, 2000),
            "settings[waitForSelectorTimeout]": random.randint(5000, 15000),
            "settings[normalizeTagNames]": random.choice([True, False]),
            "settings[trackScrollEvents]": random.choice([True, False]),
        }
        
        new_caps.update(random_settings)
        
        return new_caps
    
    def get_server_status(self) -> Dict:
        return {
            "running": self.is_running,
            "host": self.host,
            "port": self.port,
            "process_id": self.process.pid if self.process else None,
            "current_capabilities": self.current_capabilities
        }
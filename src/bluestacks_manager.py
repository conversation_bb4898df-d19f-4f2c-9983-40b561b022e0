import subprocess
import time
import os
import json
import random
from typing import Dict, Optional
import yaml
from loguru import logger


class BlueStacksManager:
    def __init__(self, config_path: str = "config/device_profiles.yaml"):
        self.config_path = config_path
        self.device_profiles = self._load_device_profiles()
        self.current_profile = None
        self.actual_device_id = None
        self.bluestacks_path = os.getenv('BLUESTACKS_PATH', '/Applications/BlueStacks.app/Contents/MacOS/BlueStacks')
        self.multi_instance_manager_path = '/Applications/BlueStacksMIM.app/Contents/MacOS/HD-MultiInstanceManager'
        self.running_instances = {}

    def _load_device_profiles(self) -> Dict:
        try:
            # Try relative to current directory first
            if os.path.exists(self.config_path):
                config_file = self.config_path
            else:
                # Try relative to project root (one level up from src)
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                config_file = os.path.join(project_root, self.config_path)

            with open(config_file, 'r') as f:
                logger.info(f"Loaded device profiles from: {config_file}")
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"Device profiles config not found: {self.config_path}")
            logger.error(f"Also tried: {config_file if 'config_file' in locals() else 'N/A'}")
            return {}

    def get_random_device_profile(self) -> Dict:
        profiles = self.device_profiles.get('device_profiles', {})
        if not profiles:
            raise ValueError("No device profiles configured")

        profile_name = random.choice(list(profiles.keys()))
        profile = profiles[profile_name].copy()

        # Randomize some properties for better anti-detection
        profile['android_id'] = self._generate_android_id()
        profile['imei'] = self._generate_imei()
        profile['advertising_id'] = self._generate_advertising_id()

        # Use actual device ID if available, otherwise use default
        if self.actual_device_id:
            profile['udid'] = self.actual_device_id
            logger.info(f"Using actual device ID: {self.actual_device_id}")
        else:
            logger.warning(f"Using default device ID: {profile.get('udid', 'emulator-5554')}")

        logger.info(f"Selected device profile: {profile_name}")
        return profile

    def _generate_android_id(self) -> str:
        return ''.join(random.choices('0123456789abcdef', k=16))

    def _generate_imei(self) -> str:
        # Generate a valid IMEI (15 digits)
        base = ''.join(random.choices('0123456789', k=14))
        check_digit = self._calculate_luhn_checksum(base)
        return base + str(check_digit)

    def _calculate_luhn_checksum(self, number: str) -> int:
        def luhn_checksum(card_num):
            def digits_of(n):
                return [int(d) for d in str(n)]
            digits = digits_of(card_num)
            odd_digits = digits[-1::-2]
            even_digits = digits[-2::-2]
            checksum = sum(odd_digits)
            for d in even_digits:
                checksum += sum(digits_of(d*2))
            return checksum % 10
        return (10 - luhn_checksum(int(number))) % 10

    def _generate_advertising_id(self) -> str:
        return f"{random.randint(10000000, 99999999):08x}-{random.randint(1000, 9999):04x}-{random.randint(1000, 9999):04x}-{random.randint(1000, 9999):04x}-{random.randint(100000000000, 999999999999):012x}"

    def configure_bluestacks_instance(self, instance_name: str = "Pixel", profile: Dict = None) -> bool:
        if profile is None:
            profile = self.get_random_device_profile()

        self.current_profile = profile

        try:
            # For modern BlueStacks, we'll use a simpler approach
            # Just ensure the instance exists and is configured
            logger.info(f"Configuring BlueStacks instance '{instance_name}' with profile: {profile['deviceName']}")

            # Store the profile for later use
            self.current_profile = profile

            # Modern BlueStacks doesn't support runtime device property changes
            # We'll focus on what we can control through ADB later
            logger.info(f"BlueStacks instance '{instance_name}' profile set (will apply via ADB)")
            return True

        except Exception as e:
            logger.error(f"Failed to configure BlueStacks instance: {e}")
            return False

    def _restart_adb_server(self) -> bool:
        """Restart ADB server to fix connection issues"""
        try:
            logger.info("Restarting ADB server...")
            subprocess.run(['adb', 'kill-server'], capture_output=True, timeout=5)
            time.sleep(2)
            subprocess.run(['adb', 'start-server'], capture_output=True, timeout=10)
            time.sleep(3)

            # Check if any device is now online
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            logger.debug(f"ADB devices after restart: {result.stdout}")
            return "device" in result.stdout.lower()
        except Exception as e:
            logger.debug(f"ADB restart failed: {e}")
            return False

    def _reconnect_device(self, device_id: str) -> bool:
        """Try to reconnect specific device"""
        try:
            logger.info(f"Attempting to reconnect device: {device_id}")
            # Extract port from device ID if it's emulator format
            if "emulator-" in device_id:
                port = device_id.split("-")[1]
                # Try to connect via TCP
                logger.debug(f"Connecting to 127.0.0.1:{port}")
                result = subprocess.run(['adb', 'connect', f'127.0.0.1:{port}'],
                                      capture_output=True, text=True, timeout=10)
                logger.debug(f"ADB connect result: {result.stdout} {result.stderr}")
                time.sleep(3)

                # Check device status
                check_result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                logger.debug(f"Device status after reconnect: {check_result.stdout}")

                # Check if device is now online
                is_online = device_id in check_result.stdout and f"{device_id}\tdevice" in check_result.stdout
                if is_online:
                    logger.info(f"Device {device_id} successfully reconnected")
                    return True
                else:
                    logger.debug(f"Device {device_id} still not online after reconnect attempt")
                    return False
            return False
        except Exception as e:
            logger.debug(f"Device reconnect failed: {e}")
            return False

    def _connect_network_device(self) -> bool:
        """Try to connect via common BlueStacks network ports"""
        try:
            logger.info("Attempting to connect via common BlueStacks ports...")
            common_ports = ['5555', '5556', '5557', '5558', '5559', '5560', '5561', '5562']
            connected_devices = []

            for port in common_ports:
                try:
                    logger.debug(f"Trying port {port}...")
                    result = subprocess.run(['adb', 'connect', f'127.0.0.1:{port}'],
                                          capture_output=True, text=True, timeout=5)
                    if "connected" in result.stdout.lower() or "already connected" in result.stdout.lower():
                        connected_devices.append(port)
                        logger.debug(f"Successfully connected to port {port}")
                    time.sleep(1)
                except Exception as e:
                    logger.debug(f"Failed to connect to port {port}: {e}")
                    continue

            if connected_devices:
                logger.info(f"Connected to ports: {connected_devices}")

            # Check if any device is now online
            time.sleep(2)
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            logger.debug(f"Devices after network connect: {result.stdout}")
            return "device" in result.stdout.lower()
        except Exception as e:
            logger.debug(f"Network connect failed: {e}")
            return False

    def _execute_bscmd(self, command: str) -> bool:
        try:
            full_command = f'"{self.bluestacks_path}" {command}'
            result = subprocess.run(full_command, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                logger.debug(f"BlueStacks command executed successfully: {command}")
                return True
            else:
                logger.warning(f"BlueStacks command failed: {command}, Error: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error(f"BlueStacks command timed out: {command}")
            return False
        except Exception as e:
            logger.error(f"Error executing BlueStacks command: {e}")
            return False

    def start_instance(self, instance_name: str = "Pixel") -> bool:
        try:
            # For modern BlueStacks, just check if it's already running
            # and assume it's available for ADB connections
            logger.info(f"Checking BlueStacks instance '{instance_name}' availability")

            # Try to check if ADB can see the device and get actual device ID
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            logger.debug(f"ADB devices output: {result.stdout}")

            device_lines = [line.strip() for line in result.stdout.split('\n') if 'emulator-' in line or '127.0.0.1:' in line]
            if device_lines:
                # Parse all devices and prioritize online ones
                available_devices = []
                for line in device_lines:
                    parts = line.split()
                    if len(parts) >= 2:
                        device_id = parts[0]
                        device_status = parts[1]
                        available_devices.append((device_id, device_status))

                # Sort devices: online devices first, then offline
                available_devices.sort(key=lambda x: (x[1] != "device", x[0]))

                # Use the first (best) available device
                device_id, device_status = available_devices[0]

                logger.info(f"Available devices: {available_devices}")
                logger.info(f"Selected device: {device_id} (status: {device_status})")

                # If selected device is offline, try to bring it online
                if device_status == "offline":
                    logger.info("Device is offline, attempting to reconnect...")
                    try:
                        # Try multiple reconnection strategies
                        strategies = [
                            # Strategy 1: Reconnect specific device first (fastest)
                            lambda: self._reconnect_device(device_id),
                            # Strategy 2: Kill and restart ADB server
                            lambda: self._restart_adb_server(),
                            # Strategy 3: Connect via network (common BlueStacks port)
                            lambda: self._connect_network_device()
                        ]

                        reconnected = False
                        for i, strategy in enumerate(strategies, 1):
                            logger.info(f"Trying reconnection strategy {i}...")
                            if strategy():
                                logger.info(f"Device {device_id} is now online")
                                reconnected = True
                                break
                            time.sleep(2)

                        if not reconnected:
                            logger.warning(f"Device {device_id} is still offline after all reconnection attempts")
                            logger.warning("This may be normal for BlueStacks - Appium can sometimes connect anyway")
                            # Try to find any online device as fallback
                            final_check = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                            online_devices = [line.split()[0] for line in final_check.stdout.split('\n')
                                            if 'device' in line and 'emulator-' in line]
                            if online_devices:
                                device_id = online_devices[0]
                                logger.info(f"Using fallback online device: {device_id}")

                    except Exception as e:
                        logger.warning(f"Failed to reconnect device: {e}")

                # Validate the selected device is actually usable
                final_check = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                if device_id in final_check.stdout:
                    final_status = "device" if f"{device_id}\tdevice" in final_check.stdout else "offline"
                    logger.info(f"Final device status: {device_id} ({final_status})")

                    # Store the actual device ID for use in profiles
                    self.actual_device_id = device_id
                    return True
                else:
                    logger.warning(f"Device {device_id} disappeared during validation")
                    self.actual_device_id = None
                    return False
            else:
                logger.warning("No BlueStacks devices found via ADB.")
                logger.info(f"Attempting to start BlueStacks instance '{instance_name}'...")

                # Try to start the BlueStacks instance
                if self.start_instance_by_name(instance_name):
                    # Wait for device to appear in ADB
                    max_wait = 30  # seconds
                    wait_interval = 2
                    for i in range(0, max_wait, wait_interval):
                        time.sleep(wait_interval)
                        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                        device_lines = [line.strip() for line in result.stdout.split('\n') if 'emulator-' in line or '127.0.0.1:' in line]
                        if device_lines:
                            for line in device_lines:
                                parts = line.split()
                                if len(parts) >= 2:
                                    device_id, device_status = parts[0], parts[1]
                                    if device_status == 'device':
                                        logger.info(f"BlueStacks instance started successfully. Device: {device_id}")
                                        self.actual_device_id = device_id
                                        return True
                        logger.info(f"Waiting for BlueStacks instance to come online... ({i+wait_interval}/{max_wait}s)")

                    logger.warning("BlueStacks instance started but device not ready within timeout")
                else:
                    logger.error(f"Failed to start BlueStacks instance '{instance_name}'")

                self.actual_device_id = None
                return False  # Return False if we can't start the instance

        except subprocess.TimeoutExpired:
            logger.warning("ADB command timed out")
            return True
        except FileNotFoundError:
            logger.warning("ADB not found. Please install Android SDK Platform Tools.")
            return True
        except Exception as e:
            logger.error(f"Error checking BlueStacks instance: {e}")
            return True

    def stop_instance(self, instance_name: str = "Pixel") -> bool:
        try:
            logger.info(f"Stopping BlueStacks instance '{instance_name}'")

            # Kill the specific instance process
            result = subprocess.run([
                'pkill', '-f', f'BlueStacks.*--instance {instance_name}'
            ], capture_output=True, text=True)

            if instance_name in self.running_instances:
                del self.running_instances[instance_name]

            logger.info(f"BlueStacks instance '{instance_name}' stopped")
            return True
        except Exception as e:
            logger.error(f"Failed to stop BlueStacks instance: {e}")
            return False

    def get_available_instances(self) -> Dict:
        """Get list of available BlueStacks instances"""
        try:
            instances = {}

            # Get running instances from process list
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            for line in result.stdout.split('\n'):
                if 'BlueStacks' in line and '--instance' in line:
                    # Extract instance name from command line
                    parts = line.split('--instance')
                    if len(parts) > 1:
                        instance_name = parts[1].strip().split()[0]
                        instances[instance_name] = {
                            'name': instance_name,
                            'status': 'running',
                            'pid': line.split()[1] if len(line.split()) > 1 else None
                        }

            # Get ADB devices to match with instances
            adb_result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            adb_devices = {}
            for line in adb_result.stdout.split('\n')[1:]:  # Skip header
                if line.strip() and '\t' in line:
                    device_id, status = line.strip().split('\t')
                    adb_devices[device_id] = status

            # Add ADB device info to instances
            for device_id, status in adb_devices.items():
                # Try to match ADB devices with instances
                found_match = False
                for instance_name in instances:
                    if not instances[instance_name].get('adb_device'):
                        instances[instance_name]['adb_device'] = device_id
                        instances[instance_name]['adb_status'] = status
                        found_match = True
                        break

                # If no running instance matches, it might be a stopped instance
                if not found_match:
                    instance_name = f"Instance_{device_id.split('-')[-1]}"
                    instances[instance_name] = {
                        'name': instance_name,
                        'status': 'stopped' if status == 'offline' else 'available',
                        'adb_device': device_id,
                        'adb_status': status
                    }

            return instances

        except Exception as e:
            logger.error(f"Failed to get available instances: {e}")
            return {}

    def create_new_instance(self, instance_name: str, android_version: str = "11") -> bool:
        """Create a new BlueStacks instance"""
        try:
            logger.info(f"Creating new BlueStacks instance: {instance_name}")

            # Note: BlueStacks instance creation typically requires GUI interaction
            # For now, we'll focus on starting existing instances
            # This would need to be implemented based on BlueStacks API if available

            logger.warning(f"Instance creation not yet implemented. Please create '{instance_name}' manually through BlueStacks Multi-Instance Manager")
            return False

        except Exception as e:
            logger.error(f"Failed to create instance {instance_name}: {e}")
            return False

    def start_instance_by_name(self, instance_name: str) -> bool:
        """Start a specific BlueStacks instance by name"""
        try:
            logger.info(f"Starting BlueStacks instance: {instance_name}")

            # Check if instance is already running
            instances = self.get_available_instances()
            if instance_name in instances and instances[instance_name]['status'] == 'running':
                logger.info(f"Instance {instance_name} is already running")
                return True

            # Start the instance
            cmd = [self.bluestacks_path, '--instance', instance_name]
            logger.info(f"Executing: {' '.join(cmd)}")

            # Start instance in background
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                start_new_session=True
            )

            # Store process info
            self.running_instances[instance_name] = {
                'process': process,
                'pid': process.pid,
                'started_at': time.time()
            }

            # Wait a bit for instance to start
            time.sleep(5)

            # Verify instance started by checking ADB
            max_retries = 12  # 1 minute total
            for i in range(max_retries):
                instances = self.get_available_instances()
                if instance_name in instances:
                    adb_status = instances[instance_name].get('adb_status')
                    if adb_status == 'device':
                        logger.info(f"Instance {instance_name} started successfully")
                        return True
                    elif adb_status == 'offline':
                        logger.info(f"Instance {instance_name} starting... (attempt {i+1}/{max_retries})")

                time.sleep(5)

            logger.warning(f"Instance {instance_name} may not have started properly")
            return True  # Return True anyway as process started

        except Exception as e:
            logger.error(f"Failed to start instance {instance_name}: {e}")
            return False

    def get_current_profile(self) -> Optional[Dict]:
        return self.current_profile

    def reset_instance(self, instance_name: str = "Pixel") -> bool:
        logger.info(f"Resetting BlueStacks instance: {instance_name}")

        try:
            # For modern BlueStacks, we'll simulate a reset by changing the profile
            # The actual device property changes will be handled via ADB in location_spoofer
            time.sleep(2)  # Simulate reset time

            # Reconfigure with new profile
            return self.configure_bluestacks_instance(instance_name)

        except Exception as e:
            logger.error(f"Failed to reset BlueStacks instance: {e}")
            return False
import random
import time
import math
from typing import Dict, List, Tuple, Optional, Callable
from appium import webdriver
try:
    from appium.webdriver.common.touch_action import TouchAction
    from appium.webdriver.common.multi_action import MultiAction
except ImportError:
    # For newer versions of Appium
    TouchAction = None
    MultiAction = None
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from loguru import logger
import numpy as np
from device_manager import DeviceProfileManager


class HumanBehaviorSimulator:
    def __init__(self):
        self.typing_speeds = {
            'slow': (0.1, 0.3),
            'normal': (0.05, 0.15),
            'fast': (0.02, 0.08)
        }
        self.current_speed = 'normal'

    def human_delay(self, min_delay: float = 0.5, max_delay: float = 2.0) -> float:
        delay = random.uniform(min_delay, max_delay)
        # Add occasional longer pauses to simulate human thinking
        if random.random() < 0.1:  # 10% chance
            delay += random.uniform(2.0, 5.0)
        return delay

    def typing_delay(self) -> float:
        min_delay, max_delay = self.typing_speeds[self.current_speed]
        return random.uniform(min_delay, max_delay)

    def generate_bezier_curve(self, start: Tuple[int, int], end: Tuple[int, int],
                             steps: int = 20) -> List[Tuple[int, int]]:
        x1, y1 = start
        x4, y4 = end

        # Generate random control points for natural curve
        x2 = x1 + random.randint(-50, 50)
        y2 = y1 + random.randint(-50, 50)
        x3 = x4 + random.randint(-50, 50)
        y3 = y4 + random.randint(-50, 50)

        points = []
        for i in range(steps + 1):
            t = i / steps

            # Bezier curve formula
            x = (1-t)**3 * x1 + 3*(1-t)**2*t * x2 + 3*(1-t)*t**2 * x3 + t**3 * x4
            y = (1-t)**3 * y1 + 3*(1-t)**2*t * y2 + 3*(1-t)*t**2 * y3 + t**3 * y4

            points.append((int(x), int(y)))

        return points

    def random_scroll_behavior(self) -> Dict:
        scroll_types = ['quick_flick', 'slow_drag', 'multi_touch']
        scroll_type = random.choice(scroll_types)

        if scroll_type == 'quick_flick':
            return {
                'type': 'flick',
                'velocity': random.randint(200, 800),
                'duration': random.randint(100, 300)
            }
        elif scroll_type == 'slow_drag':
            return {
                'type': 'drag',
                'duration': random.randint(800, 2000),
                'steps': random.randint(10, 30)
            }
        else:
            return {
                'type': 'multi_touch',
                'fingers': 2,
                'duration': random.randint(500, 1500)
            }


class StealthAutomationClient:
    def __init__(self, device_manager: DeviceProfileManager):
        self.device_manager = device_manager
        self.driver: Optional[webdriver.Remote] = None
        self.behavior_sim = HumanBehaviorSimulator()
        self.current_session_id: Optional[str] = None
        self.action_count = 0
        self.max_actions_per_session = 100

    def start_session(self, app_package: str = None, instance_name: str = "Pixel") -> bool:
        try:
            # Create new device session
            self.current_session_id, device_profile = self.device_manager.create_new_session(app_package)

            # Setup device
            success = self.device_manager.setup_device_session(self.current_session_id, instance_name)
            if not success:
                return False

            # Get Appium capabilities
            capabilities = self.device_manager.get_appium_capabilities(self.current_session_id, app_package)
            if not capabilities:
                return False

            # Try to connect to Appium driver using modern syntax
            try:
                from appium.options.android import UiAutomator2Options

                # Try both endpoint formats that Appium 2.x might use
                endpoints = [
                    f"http://{self.device_manager.appium_manager.host}:{self.device_manager.appium_manager.port}",  # Modern endpoint
                    f"http://{self.device_manager.appium_manager.host}:{self.device_manager.appium_manager.port}/wd/hub"  # Legacy endpoint
                ]

                logger.info(f"Generated capabilities: {capabilities}")

                driver_created = False
                for appium_url in endpoints:
                    try:
                        logger.info(f"Attempting connection to: {appium_url}")

                        # Create UiAutomator2Options for modern Appium
                        options = UiAutomator2Options()

                        # Set required capabilities
                        options.platform_name = capabilities.get("platformName", "Android")
                        options.platform_version = capabilities.get("platformVersion", "11")
                        options.device_name = capabilities.get("deviceName", "Android Device")
                        options.udid = capabilities.get("udid", "emulator-5554")
                        options.automation_name = capabilities.get("automationName", "UiAutomator2")
                        options.new_command_timeout = capabilities.get("newCommandTimeout", 300)
                        options.auto_grant_permissions = capabilities.get("autoGrantPermissions", True)
                        options.no_reset = capabilities.get("noReset", False)

                        # Add app package if provided
                        if capabilities.get("appPackage"):
                            options.app_package = capabilities.get("appPackage")
                            options.app_activity = capabilities.get("appActivity", "MainActivity")
                            options.auto_launch = capabilities.get("autoLaunch", True)

                        # Connect with modern syntax
                        logger.debug(f"Creating driver with UDID: {options.udid}")
                        self.driver = webdriver.Remote(appium_url, options=options)

                        # Test the connection by getting device info
                        try:
                            device_time = self.driver.get_device_time()
                            logger.info(f"Successfully connected to device. Time: {device_time}")
                            logger.info(f"Successfully connected to Appium on: {appium_url}")
                            driver_created = True
                            break
                        except Exception as test_e:
                            logger.warning(f"Driver created but device test failed: {test_e}")
                            try:
                                self.driver.quit()
                            except:
                                pass
                            self.driver = None
                            raise test_e

                    except Exception as endpoint_error:
                        logger.warning(f"Failed to connect to {appium_url}: {endpoint_error}")
                        # Check if it's a device not found error
                        if "not in the list of connected devices" in str(endpoint_error):
                            logger.error("Device not found by Appium. This usually means:")
                            logger.error("1. BlueStacks is not running")
                            logger.error("2. ADB debugging is not enabled in BlueStacks")
                            logger.error("3. Device ID mismatch between ADB and Appium")
                            logger.error(f"Expected device: {options.udid}")
                        continue

                if not driver_created:
                    logger.warning("Could not connect to Appium - enabling enhanced simulation mode")
                    logger.info("All automation actions will be simulated with realistic logging")
                    self.driver = None  # Use simulation mode

                # Set implicit wait with randomization (only if driver exists)
                if self.driver:
                    wait_time = random.randint(8, 15)
                    self.driver.implicitly_wait(wait_time)
                    logger.info("Connected to Appium driver successfully with modern syntax")
                else:
                    logger.info("Enhanced simulation mode activated - session ready for testing")

            except Exception as e:
                logger.error(f"Could not connect to Appium driver: {e}")
                logger.warning("Falling back to enhanced simulation mode")
                self.driver = None
                # Don't return False - continue with simulation mode

            logger.info(f"Automation session started: {self.current_session_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to start automation session: {e}")
            return False

    def human_tap(self, x: int, y: int, duration: int = None) -> bool:
        if not self.driver:
            logger.info(f"[SIMULATION] Human tap at ({x}, {y})")
            time.sleep(self.behavior_sim.human_delay(0.2, 0.8))
            self._increment_action()
            return True

        try:
            # Add random offset for more natural tapping
            offset_x = random.randint(-3, 3)
            offset_y = random.randint(-3, 3)

            tap_x = max(0, x + offset_x)
            tap_y = max(0, y + offset_y)

            # Random tap duration
            if duration is None:
                duration = random.randint(50, 200)

            # Pre-tap delay
            time.sleep(self.behavior_sim.human_delay(0.1, 0.5))

            # Use newer action generator if TouchAction is not available
            if TouchAction:
                action = TouchAction(self.driver)
                action.tap(None, tap_x, tap_y, 1).wait(duration).perform()
            else:
                # Use newer action approach
                from selenium.webdriver.common.actions.action_builder import ActionBuilder
                from selenium.webdriver.common.actions.pointer_input import PointerInput
                from selenium.webdriver.common.actions import interaction

                actions = ActionBuilder(self.driver)
                pointer = PointerInput(interaction.POINTER_TOUCH, "finger")
                actions.add(pointer.create_pointer_move(duration=0, x=tap_x, y=tap_y))
                actions.add(pointer.create_pointer_down())
                actions.add(pointer.create_pause(duration / 1000))
                actions.add(pointer.create_pointer_up())
                actions.perform()

            # Post-tap delay
            time.sleep(self.behavior_sim.human_delay(0.2, 0.8))

            self._increment_action()
            logger.debug(f"Human tap at ({tap_x}, {tap_y})")
            return True

        except Exception as e:
            logger.error(f"Failed to perform human tap: {e}")
            return False

    def human_swipe(self, start_x: int, start_y: int, end_x: int, end_y: int) -> bool:
        if not self.driver:
            logger.info(f"[SIMULATION] Human swipe from ({start_x}, {start_y}) to ({end_x}, {end_y})")
            time.sleep(self.behavior_sim.human_delay(0.5, 1.2))
            self._increment_action()
            return True

        try:
            # Generate curved path for natural swipe
            curve_points = self.behavior_sim.generate_bezier_curve(
                (start_x, start_y), (end_x, end_y), steps=random.randint(15, 25)
            )

            # Pre-swipe delay
            time.sleep(self.behavior_sim.human_delay(0.2, 0.8))

            if TouchAction:
                action = TouchAction(self.driver)
                action.press(None, curve_points[0][0], curve_points[0][1])

                for point in curve_points[1:]:
                    action.move_to(None, point[0], point[1])
                    time.sleep(random.uniform(0.01, 0.03))  # Small delays between moves

                action.release().perform()
            else:
                # Use newer action approach for swipe
                from selenium.webdriver.common.actions.action_builder import ActionBuilder
                from selenium.webdriver.common.actions.pointer_input import PointerInput
                from selenium.webdriver.common.actions import interaction

                actions = ActionBuilder(self.driver)
                pointer = PointerInput(interaction.POINTER_TOUCH, "finger")

                # Start touch
                actions.add(pointer.create_pointer_move(duration=0, x=curve_points[0][0], y=curve_points[0][1]))
                actions.add(pointer.create_pointer_down())

                # Move through curve points
                for point in curve_points[1:]:
                    actions.add(pointer.create_pointer_move(duration=50, x=point[0], y=point[1]))

                # End touch
                actions.add(pointer.create_pointer_up())
                actions.perform()

            # Post-swipe delay
            time.sleep(self.behavior_sim.human_delay(0.3, 1.0))

            self._increment_action()
            logger.debug(f"Human swipe from ({start_x}, {start_y}) to ({end_x}, {end_y})")
            return True

        except Exception as e:
            logger.error(f"Failed to perform human swipe: {e}")
            return False

    def human_scroll(self, direction: str = "down", distance: int = None) -> bool:
        if not self.driver:
            logger.info(f"[SIMULATION] Human scroll {direction}")
            time.sleep(self.behavior_sim.human_delay(0.3, 0.8))
            self._increment_action()
            return True

        try:
            # Get screen size
            screen_size = self.driver.get_window_size()
            width = screen_size['width']
            height = screen_size['height']

            # Calculate scroll parameters
            center_x = width // 2 + random.randint(-50, 50)

            if direction == "down":
                start_y = int(height * 0.7) + random.randint(-30, 30)
                end_y = int(height * 0.3) + random.randint(-30, 30)
            elif direction == "up":
                start_y = int(height * 0.3) + random.randint(-30, 30)
                end_y = int(height * 0.7) + random.randint(-30, 30)
            elif direction == "left":
                start_y = height // 2 + random.randint(-50, 50)
                end_y = start_y
                center_x = int(width * 0.7)
                end_x = int(width * 0.3)
                return self.human_swipe(center_x, start_y, end_x, end_y)
            elif direction == "right":
                start_y = height // 2 + random.randint(-50, 50)
                end_y = start_y
                center_x = int(width * 0.3)
                end_x = int(width * 0.7)
                return self.human_swipe(center_x, start_y, end_x, end_y)

            # Apply distance if specified
            if distance:
                if direction in ["down", "up"]:
                    end_y = start_y + (distance if direction == "down" else -distance)

            return self.human_swipe(center_x, start_y, center_x, end_y)

        except Exception as e:
            logger.error(f"Failed to perform human scroll: {e}")
            return False

    def human_type(self, text: str, element=None, clear_first: bool = True) -> bool:
        if not self.driver:
            logger.info(f"[SIMULATION] Human type: {text[:20]}...")
            # Simulate typing delay
            for char in text:
                time.sleep(self.behavior_sim.typing_delay())
            self._increment_action()
            return True

        try:
            if element and clear_first:
                element.clear()
                time.sleep(self.behavior_sim.human_delay(0.1, 0.3))

            # Type character by character with human delays
            for char in text:
                if element:
                    element.send_keys(char)
                else:
                    self.driver.press_keycode(ord(char))

                # Random typing delay
                time.sleep(self.behavior_sim.typing_delay())

                # Occasional pauses for "thinking"
                if random.random() < 0.05:  # 5% chance
                    time.sleep(random.uniform(0.5, 2.0))

            self._increment_action()
            logger.debug(f"Human typed: {text[:10]}...")
            return True

        except Exception as e:
            logger.error(f"Failed to perform human typing: {e}")
            return False

    def find_element_smart(self, by: By, value: str, timeout: int = 10,
                          retry_on_fail: bool = True) -> Optional:
        if not self.driver:
            logger.info(f"[SIMULATION] Finding element: {value}")
            time.sleep(self.behavior_sim.human_delay(0.2, 0.6))
            return None  # Return None in simulation mode

        try:
            wait = WebDriverWait(self.driver, timeout)
            element = wait.until(EC.presence_of_element_located((by, value)))

            # Add human delay before interacting
            time.sleep(self.behavior_sim.human_delay(0.2, 0.6))
            return element

        except TimeoutException:
            if retry_on_fail:
                logger.warning(f"Element not found, attempting scroll and retry: {value}")
                # Try scrolling and searching again
                self.human_scroll("down")
                time.sleep(1)
                return self.find_element_smart(by, value, timeout=5, retry_on_fail=False)

            logger.error(f"Element not found: {value}")
            return None
        except Exception as e:
            logger.error(f"Error finding element: {e}")
            return None

    def wait_and_tap(self, by: By, value: str, timeout: int = 10) -> bool:
        element = self.find_element_smart(by, value, timeout)
        if element:
            location = element.location
            size = element.size

            # Calculate center point with random offset
            center_x = location['x'] + size['width'] // 2
            center_y = location['y'] + size['height'] // 2

            return self.human_tap(center_x, center_y)
        return False

    def _increment_action(self):
        self.action_count += 1
        if self.current_session_id:
            self.device_manager.increment_action_count(self.current_session_id)

    def should_rotate_session(self) -> bool:
        if not self.current_session_id:
            return True
        return self.device_manager.should_rotate_session(self.current_session_id)

    def rotate_session(self, app_package: str = None, instance_name: str = "Pixel") -> bool:
        if not self.current_session_id:
            return self.start_session(app_package, instance_name)

        try:
            logger.info("Rotating automation session for anti-detection")

            # Close current driver
            if self.driver:
                self.driver.quit()
                self.driver = None

            # Rotate device session
            self.current_session_id, device_profile = self.device_manager.rotate_device_session(
                self.current_session_id, instance_name, app_package
            )

            # Get new capabilities and reconnect
            capabilities = self.device_manager.get_appium_capabilities(self.current_session_id, app_package)
            appium_url = f"http://{self.device_manager.appium_manager.host}:{self.device_manager.appium_manager.port}/wd/hub"

            # Wait for setup to complete
            time.sleep(10)

            self.driver = webdriver.Remote(appium_url, capabilities)
            self.driver.implicitly_wait(random.randint(8, 15))

            # Reset action count
            self.action_count = 0

            logger.info(f"Session rotated successfully: {self.current_session_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to rotate session: {e}")
            return False

    def end_session(self):
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None

            if self.current_session_id:
                self.device_manager.end_session(self.current_session_id)
                self.current_session_id = None

            logger.info("Automation session ended")

        except Exception as e:
            logger.error(f"Error ending session: {e}")

    def get_session_info(self, for_database: bool = False) -> Optional[Dict]:
        if self.current_session_id:
            return self.device_manager.get_session_info(self.current_session_id, for_database)
        return None

    def perform_random_human_activity(self, duration: int = 30):
        """Perform random human-like activities for specified duration"""
        start_time = time.time()
        activities = ['scroll', 'tap_random', 'swipe', 'pause']

        logger.info(f"[SIMULATION] Starting random human activity for {duration} seconds")

        while time.time() - start_time < duration:
            activity = random.choice(activities)

            if activity == 'scroll':
                direction = random.choice(['up', 'down', 'left', 'right'])
                self.human_scroll(direction)
            elif activity == 'tap_random':
                if self.driver:
                    screen_size = self.driver.get_window_size()
                    x = random.randint(50, screen_size['width'] - 50)
                    y = random.randint(50, screen_size['height'] - 50)
                else:
                    # Use default screen size for simulation
                    x = random.randint(50, 350)
                    y = random.randint(50, 750)
                self.human_tap(x, y)
            elif activity == 'swipe':
                if self.driver:
                    screen_size = self.driver.get_window_size()
                    start_x = random.randint(50, screen_size['width'] - 50)
                    start_y = random.randint(50, screen_size['height'] - 50)
                    end_x = random.randint(50, screen_size['width'] - 50)
                    end_y = random.randint(50, screen_size['height'] - 50)
                else:
                    # Use default coordinates for simulation
                    start_x = random.randint(50, 350)
                    start_y = random.randint(50, 750)
                    end_x = random.randint(50, 350)
                    end_y = random.randint(50, 750)
                self.human_swipe(start_x, start_y, end_x, end_y)
            elif activity == 'pause':
                logger.info("[SIMULATION] Pausing...")
                time.sleep(random.uniform(2, 8))

            # Random delay between activities
            time.sleep(random.uniform(1, 4))

        logger.info("[SIMULATION] Random human activity completed")